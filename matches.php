<?php
// Set page title
$page_title = 'Matches';

// Include header
require_once 'includes/header.php';

// Process upload proof form
$transaction_id = '';
$transaction_id_err = $file_err = '';
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['upload_proof'])) {
    // Validate transaction ID (now optional)
    if (!empty(trim($_POST['transaction_id']))) {
        $transaction_id = sanitize($_POST['transaction_id']);
    } else {
        $transaction_id = ''; // Empty string for optional transaction ID
    }

    // Validate file upload
    if (!isset($_FILES['proof_file']) || $_FILES['proof_file']['error'] == UPLOAD_ERR_NO_FILE) {
        $file_err = 'Please upload proof of payment.';
    } else {
        $upload_result = upload_file($_FILES['proof_file'], 'uploads/proofs/');

        if (!$upload_result['success']) {
            $file_err = $upload_result['message'];
        }
    }

    // If no errors, process the upload
    if (empty($file_err)) {
        // Get match ID
        $match_id = $_POST['match_id'];

        // Update match
        $query = "UPDATE matches SET status = 'payment_sent', proof_file = :proof_file, transaction_id = :transaction_id, updated_at = NOW() WHERE id = :id AND sender_id = :user_id AND status = 'pending'";
        $stmt = $db->prepare($query);

        $stmt->bindParam(':proof_file', $upload_result['filename']);
        $stmt->bindParam(':transaction_id', $transaction_id);
        $stmt->bindParam(':id', $match_id);
        $stmt->bindParam(':user_id', $user_id);

        if ($stmt->execute()) {
            // Get match details
            $query = "SELECT * FROM matches WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $match_id);
            $stmt->execute();
            $match = $stmt->fetch(PDO::FETCH_OBJ);

            // Create notification for sender
            create_notification($user_id, 'Payment Sent', 'You have sent payment for match #' . $match_id . '.', 'match', $db);

            // Create notification for receiver
            create_notification($match->receiver_id, 'Payment Received', 'You have received payment for match #' . $match_id . '. Please confirm receipt.', 'match', $db);

            // Set success message
            $success_message = 'Your payment proof has been uploaded successfully.';

            // Clear form data
            $transaction_id = '';
        } else {
            $error_message = 'Something went wrong. Please try again.';
        }
    }
}

// Process confirm receipt form
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['confirm_receipt'])) {
    // Get match ID
    $match_id = $_POST['match_id'];

    // Start transaction
    $db->beginTransaction();

    try {
        // Update match
        $query = "UPDATE matches SET status = 'completed', updated_at = NOW() WHERE id = :id AND receiver_id = :user_id AND status = 'payment_sent'";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $match_id);
        $stmt->bindParam(':user_id', $user_id);

        if (!$stmt->execute()) {
            throw new Exception("Failed to update match status");
        }

        // Get match details
        $query = "SELECT * FROM matches WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $match_id);
        $stmt->execute();
        $match = $stmt->fetch(PDO::FETCH_OBJ);

        if (!$match) {
            throw new Exception("Match not found");
        }

        // Update pledge status
        $query = "UPDATE pledges SET status = 'completed', updated_at = NOW() WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $match->pledge_id);

        if (!$stmt->execute()) {
            throw new Exception("Failed to update pledge status");
        }

        // Add sender to receivers queue if not already in queue using amount-aware system
        $double_amount = $match->amount * 2;
        $query = "UPDATE users SET
                  pledges_to_receive = 2,
                  amount_to_receive = :amount_to_receive,
                  original_pledge_amount = :original_amount,
                  updated_at = NOW()
                  WHERE id = :user_id AND (pledges_to_receive = 0 OR pledges_to_receive IS NULL)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $match->sender_id);
        $stmt->bindParam(':amount_to_receive', $double_amount);
        $stmt->bindParam(':original_amount', $match->amount);
        $stmt->execute();

        // Get receiver details for amount-aware processing
        $query = "SELECT pledges_to_receive, amount_to_receive, original_pledge_amount FROM users WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $match->receiver_id);
        $stmt->execute();
        $receiver = $stmt->fetch(PDO::FETCH_OBJ);

        if ($receiver && $receiver->pledges_to_receive > 0) {
            // Use amount-aware processing
            if ($receiver->amount_to_receive > 0) {
                // New amount-aware system: track actual amounts received
                $new_amount_to_receive = max(0, $receiver->amount_to_receive - $match->amount);
                $new_count = $receiver->pledges_to_receive - 1;

                // Check if user has received their full double amount
                if ($new_amount_to_receive <= 0) {
                    // User has received their full double amount, remove from queue
                    $query = "UPDATE users SET
                              pledges_to_receive = 0,
                              amount_to_receive = 0,
                              updated_at = NOW()
                              WHERE id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $match->receiver_id);
                    $stmt->execute();

                    $remaining_message = "You have received all your pledges (GHS " . $receiver->original_pledge_amount . " × 2 = GHS " . ($receiver->original_pledge_amount * 2) . ").";
                } else {
                    // User still needs more to reach double amount
                    if ($new_count == 0) {
                        // This was their second pledge but they still need more money
                        // Keep them in queue but update timestamp to move to back
                        $query = "UPDATE users SET
                                  pledges_to_receive = 1,
                                  amount_to_receive = :new_amount,
                                  updated_at = NOW()
                                  WHERE id = :user_id";
                    } else {
                        // First pledge, keep at front of queue
                        $query = "UPDATE users SET
                                  pledges_to_receive = :new_count,
                                  amount_to_receive = :new_amount
                                  WHERE id = :user_id";
                    }
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':new_count', $new_count);
                    $stmt->bindParam(':new_amount', $new_amount_to_receive);
                    $stmt->bindParam(':user_id', $match->receiver_id);
                    $stmt->execute();

                    $remaining_message = "You still need GHS " . $new_amount_to_receive . " more to reach your double amount (GHS " . ($receiver->original_pledge_amount * 2) . ").";
                }
            } else {
                // Legacy count-based system for users without amount tracking
                $new_count = $receiver->pledges_to_receive - 1;

                // Only update the updated_at timestamp if this is their second pledge (new_count becomes 0)
                // This allows users to receive two pledges consecutively before being moved to the back of the queue
                if ($new_count == 0) {
                    // When the user has received their second pledge, update the timestamp to move them to the back of the queue
                    $query = "UPDATE users SET
                              pledges_to_receive = :new_count,
                              updated_at = NOW()
                              WHERE id = :user_id";
                } else {
                    // For the first pledge, don't update the timestamp to keep them at the front of the queue
                    $query = "UPDATE users SET
                              pledges_to_receive = :new_count
                              WHERE id = :user_id";
                }

                $stmt = $db->prepare($query);
                $stmt->bindParam(':new_count', $new_count);
                $stmt->bindParam(':user_id', $match->receiver_id);
                $stmt->execute();

                $remaining_message = ($new_count > 0 ? "You are still in queue to receive $new_count more pledge(s)." : "You have received all your pledges.");
            }

            // Create notification for receiver
            create_notification($match->receiver_id, 'Pledge Received',
                'You have received a pledge of GHS ' . $match->amount . '. ' . $remaining_message,
                'pledge', $db);
        }

        // Create notification for sender
        create_notification($match->sender_id, 'Payment Confirmed',
            'Your payment for match #' . $match_id . ' has been confirmed by the receiver. You are now in queue to receive GHS ' . ($match->amount * 2) . ' (double your pledge of GHS ' . $match->amount . ').',
            'match', $db);

        // Commit the transaction
        $db->commit();

        $success_message = 'Payment confirmed successfully!';
    } catch (Exception $e) {
        // Rollback the transaction
        $db->rollBack();
        $error_message = 'Error: ' . $e->getMessage();
    }
}

// Get current matches as sender
$query = "SELECT m.*, p.amount, u.name as receiver_name, u.mobile_number, u.mobile_name
          FROM matches m
          JOIN pledges p ON m.pledge_id = p.id
          JOIN users u ON m.receiver_id = u.id
          WHERE m.sender_id = :user_id AND m.status IN ('pending', 'payment_sent')
          ORDER BY m.created_at DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$sender_matches = $stmt->fetchAll(PDO::FETCH_OBJ);

// Get current matches as receiver
$query = "SELECT m.*, p.amount, u.name as sender_name
          FROM matches m
          JOIN pledges p ON m.pledge_id = p.id
          JOIN users u ON m.sender_id = u.id
          WHERE m.receiver_id = :user_id AND m.status IN ('pending', 'payment_sent')
          ORDER BY m.created_at DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$receiver_matches = $stmt->fetchAll(PDO::FETCH_OBJ);

// Get match history
$query = "SELECT m.*, p.amount,
          CASE WHEN m.sender_id = :user_id THEN u.name ELSE us.name END as other_party_name,
          CASE WHEN m.sender_id = :user_id THEN 'sender' ELSE 'receiver' END as role
          FROM matches m
          JOIN pledges p ON m.pledge_id = p.id
          JOIN users u ON m.receiver_id = u.id
          JOIN users us ON m.sender_id = us.id
          WHERE (m.sender_id = :user_id OR m.receiver_id = :user_id) AND m.status IN ('completed', 'disputed', 'cancelled')
          ORDER BY m.updated_at DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$match_history = $stmt->fetchAll(PDO::FETCH_OBJ);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Matches</h1>
    </div>

    <!-- Display flash messages from redirects -->
    <?php echo flash_message('match_success'); ?>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>

    <!-- Matches as Sender -->
    <?php if (!empty($sender_matches)): ?>
        <?php foreach ($sender_matches as $match): ?>
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Current Match (You are the Sender)</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Receiver Details</h6>
                            <div class="table-responsive">
                                <table class="table table-bordered table-mobile-responsive">
                                    <tr>
                                        <th data-label="Name">Name</th>
                                        <td data-label="Name"><?php echo $match->receiver_name; ?></td>
                                    </tr>
                                    <tr>
                                        <th data-label="Mobile Number">Mobile Number</th>
                                        <td data-label="Mobile Number">
                                            <div class="d-flex align-items-center">
                                                <span class="mr-2"><?php echo $match->mobile_number; ?></span>
                                                <button class="btn btn-sm btn-outline-secondary copy-btn" data-clipboard-text="<?php echo $match->mobile_number; ?>">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th data-label="Mobile Name">Mobile Name</th>
                                        <td data-label="Mobile Name"><?php echo $match->mobile_name; ?></td>
                                    </tr>
                                    <tr>
                                        <th data-label="Amount">Amount</th>
                                        <td data-label="Amount"><?php echo format_currency($match->amount, 'GHS'); ?></td>
                                    </tr>
                                    <tr>
                                        <th data-label="Deadline">Deadline</th>
                                        <td data-label="Deadline">
                                        <?php
                                        $deadline = new DateTime($match->deadline);
                                        $now = new DateTime();
                                        $interval = $now->diff($deadline);
                                        $hours_remaining = ($interval->days * 24) + $interval->h;

                                        echo format_date($match->deadline);

                                        if ($deadline > $now) {
                                            echo ' <span class="badge badge-warning">' . $hours_remaining . ' hours remaining</span>';
                                        } else {
                                            echo ' <span class="badge badge-danger">Expired</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>

                            <div class="alert alert-info">
                                <p class="mb-0">Please send the payment to the receiver using the mobile number provided above, then upload proof of payment.</p>
                            </div>

                            <a href="chat.php?match_id=<?php echo $match->id; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-comments"></i> Chat with Receiver
                            </a>
                        </div>

                        <div class="col-md-6">
                            <?php if ($match->status == 'pending'): ?>
                                <h6>Upload Payment Proof</h6>
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="match_id" value="<?php echo $match->id; ?>">

                                    <div class="form-group">
                                        <label for="transaction_id">Transaction ID / Reference (Optional)</label>
                                        <input type="text" name="transaction_id" class="form-control <?php echo (!empty($transaction_id_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $transaction_id; ?>">
                                        <span class="invalid-feedback"><?php echo $transaction_id_err; ?></span>
                                        <small class="form-text text-muted">You can leave this field empty if you don't have a transaction ID.</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="proof_file">Upload Proof of Payment</label>
                                        <div class="custom-file">
                                            <input type="file" name="proof_file" class="custom-file-input <?php echo (!empty($file_err)) ? 'is-invalid' : ''; ?>" id="proof_file" accept=".jpg,.jpeg,.png,.pdf">
                                            <label class="custom-file-label" for="proof_file">Choose file</label>
                                            <span class="invalid-feedback"><?php echo $file_err; ?></span>
                                        </div>
                                        <small class="form-text text-muted">Accepted formats: JPG, PNG, PDF. Max size: 5MB</small>
                                    </div>

                                    <div class="form-group">
                                        <img id="image-preview" class="img-fluid mb-3" style="display: none; max-height: 200px;">
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" name="upload_proof" class="btn btn-primary">Submit Proof</button>
                                    </div>
                                </form>
                            <?php else: ?>
                                <h6>Payment Sent</h6>
                                <div class="alert alert-success">
                                    <p>You have sent the payment. Waiting for the receiver to confirm receipt.</p>
                                    <?php if (!empty($match->transaction_id)): ?>
                                        <p><strong>Transaction ID:</strong> <?php echo $match->transaction_id; ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($match->proof_file)): ?>
                                        <p><strong>Proof:</strong> <a href="uploads/proofs/<?php echo $match->proof_file; ?>" target="_blank">View Proof</a></p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Matches as Receiver -->
    <?php if (!empty($receiver_matches)): ?>
        <?php foreach ($receiver_matches as $match): ?>
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Current Match (You are the Receiver)</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Match Details</h6>
                            <div class="table-responsive">
                                <table class="table table-bordered table-mobile-responsive">
                                    <tr>
                                        <th data-label="Sender">Sender</th>
                                        <td data-label="Sender"><?php echo $match->sender_name; ?></td>
                                    </tr>
                                    <tr>
                                        <th data-label="Amount">Amount</th>
                                        <td data-label="Amount"><?php echo format_currency($match->amount, 'GHS'); ?></td>
                                    </tr>
                                    <tr>
                                        <th data-label="Status">Status</th>
                                        <td data-label="Status">
                                            <?php
                                            switch ($match->status) {
                                                case 'pending':
                                                    echo '<span class="badge badge-warning">Waiting for Payment</span>';
                                                    break;
                                                case 'payment_sent':
                                                    echo '<span class="badge badge-info">Payment Sent</span>';
                                                    break;
                                                default:
                                                    echo '<span class="badge badge-secondary">Unknown</span>';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th data-label="Deadline">Deadline</th>
                                        <td data-label="Deadline">
                                        <?php
                                        $deadline = new DateTime($match->deadline);
                                        $now = new DateTime();
                                        $interval = $now->diff($deadline);
                                        $hours_remaining = ($interval->days * 24) + $interval->h;

                                        echo format_date($match->deadline);

                                        if ($deadline > $now) {
                                            echo ' <span class="badge badge-warning">' . $hours_remaining . ' hours remaining</span>';
                                        } else {
                                            echo ' <span class="badge badge-danger">Expired</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>

                            <a href="chat.php?match_id=<?php echo $match->id; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-comments"></i> Chat with Sender
                            </a>
                        </div>

                        <div class="col-md-6">
                            <?php if ($match->status == 'pending'): ?>
                                <div class="alert alert-info">
                                    <h6>Waiting for Payment</h6>
                                    <p>The sender has been matched with you and should send the payment soon.</p>
                                    <p>You will be notified when the payment is sent.</p>
                                </div>
                            <?php else: ?>
                                <h6>Payment Received</h6>
                                <div class="alert alert-success">
                                    <p>The sender has sent the payment. Please check if you have received it.</p>
                                    <?php if (!empty($match->transaction_id)): ?>
                                        <p><strong>Transaction ID:</strong> <?php echo $match->transaction_id; ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($match->proof_file)): ?>
                                        <p><strong>Proof:</strong> <a href="uploads/proofs/<?php echo $match->proof_file; ?>" target="_blank">View Proof</a></p>
                                    <?php endif; ?>
                                </div>

                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                                    <input type="hidden" name="match_id" value="<?php echo $match->id; ?>">
                                    <div class="form-group">
                                        <button type="submit" name="confirm_receipt" class="btn btn-success" onclick="return confirm('Are you sure you have received the payment?')">
                                            <i class="fas fa-check"></i> Confirm Receipt
                                        </button>
                                        <a href="dispute.php?match_id=<?php echo $match->id; ?>" class="btn btn-danger">
                                            <i class="fas fa-exclamation-triangle"></i> Report Problem
                                        </a>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>

    <?php if (empty($sender_matches) && empty($receiver_matches)): ?>
        <div class="alert alert-info">
            <p class="mb-0">You don't have any active matches at the moment.</p>
        </div>
    <?php endif; ?>

    <!-- Match History -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Match History</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-mobile-responsive">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Role</th>
                            <th>Other Party</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($match_history)): ?>
                            <tr>
                                <td colspan="6" class="text-center">No match history found</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($match_history as $match): ?>
                                <tr>
                                    <td data-label="Date"><?php echo format_date($match->created_at); ?></td>
                                    <td data-label="Amount"><?php echo format_currency($match->amount, 'GHS'); ?></td>
                                    <td data-label="Role">
                                        <?php echo ucfirst($match->role); ?>
                                    </td>
                                    <td data-label="Other Party"><?php echo $match->other_party_name; ?></td>
                                    <td data-label="Status">
                                        <?php
                                        switch ($match->status) {
                                            case 'completed':
                                                echo '<span class="badge badge-success">Completed</span>';
                                                break;
                                            case 'disputed':
                                                echo '<span class="badge badge-danger">Disputed</span>';
                                                break;
                                            case 'cancelled':
                                                echo '<span class="badge badge-secondary">Cancelled</span>';
                                                break;
                                            default:
                                                echo '<span class="badge badge-secondary">Unknown</span>';
                                        }
                                        ?>
                                    </td>
                                    <td data-label="Actions">
                                        <div class="btn-group">
                                            <a href="chat.php?match_id=<?php echo $match->id; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-comments"></i> <span class="d-none d-md-inline">Chat</span>
                                            </a>
                                            <?php if (!empty($match->proof_file)): ?>
                                                <a href="uploads/proofs/<?php echo $match->proof_file; ?>" target="_blank" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-file-alt"></i> <span class="d-none d-md-inline">Proof</span>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
