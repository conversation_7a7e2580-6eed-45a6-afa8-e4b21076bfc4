<?php
/**
 * Test script to verify the queue system fix
 * This script tests that users receive twice consecutively before being removed from the queue
 */

require_once 'includes/config.php';
require_once 'includes/pledge_system.php';

echo "=== P2P Donate Queue System Fix Test ===\n\n";

// Test scenario: User should receive twice consecutively before being removed from queue
echo "Testing queue system with amount-aware logic...\n\n";

try {
    // Create a test user in the queue
    $test_user_id = 1; // Assuming user ID 1 exists
    $pledge_amount = 50; // GHS 50 pledge
    $double_amount = $pledge_amount * 2; // GHS 100 to receive
    
    echo "1. Adding test user to queue to receive GHS $double_amount (double of GHS $pledge_amount)...\n";
    
    // Add user to queue with amount-aware system
    $query = "UPDATE users SET
              pledges_to_receive = 2,
              amount_to_receive = :amount_to_receive,
              original_pledge_amount = :original_amount,
              updated_at = NOW()
              WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $test_user_id);
    $stmt->bindParam(':amount_to_receive', $double_amount);
    $stmt->bindParam(':original_amount', $pledge_amount);
    
    if ($stmt->execute()) {
        echo "   ✓ User added to queue successfully\n";
    } else {
        echo "   ✗ Failed to add user to queue\n";
        exit(1);
    }
    
    // Check initial queue status
    $query = "SELECT pledges_to_receive, amount_to_receive, original_pledge_amount FROM users WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $test_user_id);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_OBJ);
    
    echo "   Initial status: pledges_to_receive={$user->pledges_to_receive}, amount_to_receive={$user->amount_to_receive}\n\n";
    
    // Simulate first pledge received (GHS 30)
    $first_pledge_amount = 30;
    echo "2. Simulating first pledge received: GHS $first_pledge_amount...\n";
    
    $new_amount_to_receive = max(0, $user->amount_to_receive - $first_pledge_amount);
    $new_count = $user->pledges_to_receive - 1;
    
    if ($new_amount_to_receive <= 0) {
        // User has received their full double amount, remove from queue
        $query = "UPDATE users SET
                  pledges_to_receive = 0,
                  amount_to_receive = 0,
                  updated_at = NOW()
                  WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $test_user_id);
        $stmt->execute();
        
        echo "   ✓ User removed from queue (received full amount)\n";
        $user_still_in_queue = false;
    } else {
        // User still needs more money, keep at front of queue
        $query = "UPDATE users SET
                  pledges_to_receive = :new_count,
                  amount_to_receive = :new_amount
                  WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':new_count', $new_count);
        $stmt->bindParam(':new_amount', $new_amount_to_receive);
        $stmt->bindParam(':user_id', $test_user_id);
        $stmt->execute();
        
        echo "   ✓ User kept in queue (still needs GHS $new_amount_to_receive)\n";
        $user_still_in_queue = true;
    }
    
    // Check status after first pledge
    $query = "SELECT pledges_to_receive, amount_to_receive FROM users WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $test_user_id);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_OBJ);
    
    echo "   Status after first pledge: pledges_to_receive={$user->pledges_to_receive}, amount_to_receive={$user->amount_to_receive}\n\n";
    
    if ($user_still_in_queue) {
        // Simulate second pledge received (remaining amount)
        $second_pledge_amount = $new_amount_to_receive;
        echo "3. Simulating second pledge received: GHS $second_pledge_amount...\n";
        
        $final_amount_to_receive = max(0, $user->amount_to_receive - $second_pledge_amount);
        $final_count = $user->pledges_to_receive - 1;
        
        if ($final_amount_to_receive <= 0) {
            // User has received their full double amount, remove from queue
            $query = "UPDATE users SET
                      pledges_to_receive = 0,
                      amount_to_receive = 0,
                      updated_at = NOW()
                      WHERE id = :user_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $test_user_id);
            $stmt->execute();
            
            echo "   ✓ User removed from queue (received full double amount)\n";
        } else {
            echo "   ⚠ User still in queue (unexpected - should have received full amount)\n";
        }
        
        // Check final status
        $query = "SELECT pledges_to_receive, amount_to_receive FROM users WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $test_user_id);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_OBJ);
        
        echo "   Final status: pledges_to_receive={$user->pledges_to_receive}, amount_to_receive={$user->amount_to_receive}\n\n";
    }
    
    echo "=== Test Results ===\n";
    if ($user->pledges_to_receive == 0 && $user->amount_to_receive == 0) {
        echo "✓ SUCCESS: User was properly removed from queue after receiving double amount\n";
        echo "✓ The queue system now works correctly with amount-aware logic\n";
        echo "✓ Users will receive twice consecutively before being removed\n";
    } else {
        echo "✗ FAILURE: User is still in queue unexpectedly\n";
        echo "  pledges_to_receive: {$user->pledges_to_receive}\n";
        echo "  amount_to_receive: {$user->amount_to_receive}\n";
    }
    
} catch (Exception $e) {
    echo "Error during test: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
