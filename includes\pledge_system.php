<?php
/**
 * Pledge System Functions
 *
 * This file contains functions for the "give one, receive two" pledge system
 * where users make a fixed GHS pledge and receive two pledges in return.
 *
 * Note: PLEDGE_AMOUNT is defined in config/config.php
 */

// Check if PLEDGE_AMOUNT is defined, if not, define it with a default value
if (!defined('PLEDGE_AMOUNT')) {
    // This is a fallback in case config/config.php is not included before this file
    require_once __DIR__ . '/../config/config.php';
}

/**
 * Create a new pledge for a user
 *
 * @param PDO $db Database connection
 * @param int $user_id User ID
 * @param string $pledge_amount (Optional) The amount to pledge, defaults to PLED<PERSON>_AMOUNT
 * @return array Result with status and message
 */
function create_pledge($db, $user_id, $pledge_amount = null) {
    // Platform fee in tokens
    $platform_fee = 10;

    // If no pledge amount is provided, use the default
    if ($pledge_amount === null) {
        $pledge_amount = PLEDGE_AMOUNT;
    }

    // Validate pledge amount is one of the allowed values
    $valid_amounts = [PLEDGE_AMOUNT_MICRO, PLEDGE_AMOUNT_SMALL, PLEDGE_AMOUNT_MEDIUM, PLEDGE_AMOUNT_LARGE];
    if (!in_array($pledge_amount, $valid_amounts)) {
        return [
            'status' => false,
            'message' => 'Invalid pledge amount. Please select a valid amount.'
        ];
    }

    // Check if user exists and has enough tokens
    $query = "SELECT * FROM users WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_OBJ);

    if (!$user) {
        return [
            'status' => false,
            'message' => 'User not found'
        ];
    }

    // Check if user has enough tokens for the platform fee
    if ($user->token_balance < $platform_fee) {
        return [
            'status' => false,
            'message' => 'Insufficient tokens. You need ' . $platform_fee . ' tokens to make a pledge.'
        ];
    }

    // Start transaction
    $db->beginTransaction();

    try {
        // Create pledge record
        $query = "INSERT INTO pledges (user_id, amount, status, currency)
                  VALUES (:user_id, :amount, 'pending', 'GHS')";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':amount', $pledge_amount);
        $stmt->execute();
        $pledge_id = $db->lastInsertId();

        // Deduct platform fee from user's token balance
        $query = "UPDATE users SET token_balance = token_balance - :fee WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':fee', $platform_fee);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        // Record token transaction
        $query = "INSERT INTO tokens (user_id, amount, transaction_type, status, reference)
                  VALUES (:user_id, :amount, 'pledge', 'confirmed', 'Platform fee for pledge #" . $pledge_id . "')";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':amount', $platform_fee);
        $stmt->execute();

        // Commit transaction
        $db->commit();

        return [
            'status' => true,
            'message' => 'Pledge created successfully',
            'pledge_id' => $pledge_id
        ];
    } catch (Exception $e) {
        // Rollback transaction
        $db->rollBack();

        return [
            'status' => false,
            'message' => 'Error creating pledge: ' . $e->getMessage()
        ];
    }
}

/**
 * Match a pledger with a receiver using amount-aware matching
 *
 * @param PDO $db Database connection
 * @param int $pledge_id Pledge ID
 * @return array Result with status and message
 */
function match_pledge($db, $pledge_id) {
    // Get pledge details
    $query = "SELECT * FROM pledges WHERE id = :pledge_id AND status = 'pending'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':pledge_id', $pledge_id);
    $stmt->execute();
    $pledge = $stmt->fetch(PDO::FETCH_OBJ);

    if (!$pledge) {
        return [
            'status' => false,
            'message' => 'Pledge not found or not in pending status'
        ];
    }

    // Find a receiver using amount-aware FIFO matching
    // Priority 1: Exact amount match (receiver needs exactly this pledge amount)
    // Priority 2: Partial match (receiver needs more than this pledge amount)
    // Priority 3: Over-match (receiver needs less, but we can partially fulfill)

    $receiver = find_best_receiver($db, $pledge->user_id, $pledge->amount);

    // If no eligible receiver found
    if (!$receiver) {
        return [
            'status' => false,
            'message' => 'No eligible receiver found in the queue. All users in queue must have made at least one pledge.'
        ];
    }

    // Start transaction
    $db->beginTransaction();

    try {
        // Process the match with amount-aware logic
        $match_result = process_amount_aware_match($db, $pledge, $receiver);

        if (!$match_result['status']) {
            $db->rollBack();
            return $match_result;
        }
        // Update pledge status
        $query = "UPDATE pledges SET status = 'matched' WHERE id = :pledge_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':pledge_id', $pledge_id);
        $stmt->execute();

        // Set deadline (48 hours from now)
        $deadline = date('Y-m-d H:i:s', strtotime('+48 hours'));

        // Create match
        $query = "INSERT INTO matches (pledge_id, sender_id, receiver_id, amount, deadline, currency, status)
                  VALUES (:pledge_id, :sender_id, :receiver_id, :amount, :deadline, 'GHS', 'pending')";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':pledge_id', $pledge_id);
        $stmt->bindParam(':sender_id', $pledge->user_id);
        $stmt->bindParam(':receiver_id', $receiver->id);
        $stmt->bindParam(':amount', $pledge->amount);
        $stmt->bindParam(':deadline', $deadline);
        $stmt->execute();
        $match_id = $db->lastInsertId();

        // Create notifications
        create_notification($pledge->user_id, 'Match Created',
            'You have been matched with a receiver for your pledge of GHS ' . $pledge->amount . '.', 'match', $db);
        create_notification($receiver->id, 'Match Created',
            'You have been matched with a sender for a donation of GHS ' . $pledge->amount . '.', 'match', $db);

        // Commit transaction
        $db->commit();

        return [
            'status' => true,
            'message' => 'Pledge matched successfully',
            'match_id' => $match_id,
            'receiver_remaining' => $match_result['receiver_remaining']
        ];
    } catch (Exception $e) {
        // Rollback transaction
        $db->rollBack();

        return [
            'status' => false,
            'message' => 'Error matching pledge: ' . $e->getMessage()
        ];
    }
}

/**
 * Confirm a pledge payment
 *
 * @param PDO $db Database connection
 * @param int $match_id Match ID
 * @return array Result with status and message
 */
function confirm_pledge_payment($db, $match_id) {
    // Get match details
    $query = "SELECT * FROM matches WHERE id = :match_id AND status = 'payment_sent'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':match_id', $match_id);
    $stmt->execute();
    $match = $stmt->fetch(PDO::FETCH_OBJ);

    if (!$match) {
        return [
            'status' => false,
            'message' => 'Match not found or payment not sent'
        ];
    }

    // Start transaction
    $db->beginTransaction();

    try {
        // Update match status
        $query = "UPDATE matches SET status = 'completed', updated_at = NOW() WHERE id = :match_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':match_id', $match_id);
        $stmt->execute();

        // Update pledge status
        $query = "UPDATE pledges SET status = 'completed', updated_at = NOW() WHERE id = :pledge_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':pledge_id', $match->pledge_id);
        $stmt->execute();

        // Add sender to receivers queue if not already in queue using amount-aware system
        $double_amount = $match->amount * 2;
        $query = "UPDATE users SET
                  pledges_to_receive = 2,
                  amount_to_receive = :amount_to_receive,
                  original_pledge_amount = :original_amount,
                  updated_at = NOW()
                  WHERE id = :user_id AND (pledges_to_receive = 0 OR pledges_to_receive IS NULL)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $match->sender_id);
        $stmt->bindParam(':amount_to_receive', $double_amount);
        $stmt->bindParam(':original_amount', $match->amount);
        $stmt->execute();

        // Get receiver's current queue status
        $query = "SELECT pledges_to_receive, amount_to_receive, original_pledge_amount FROM users WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $match->receiver_id);
        $stmt->execute();
        $receiver = $stmt->fetch(PDO::FETCH_OBJ);

        // Update receiver's queue status if they are in the queue
        if ($receiver && $receiver->pledges_to_receive > 0) {
            // Use amount-aware logic if available
            if ($receiver->amount_to_receive > 0) {
                $new_amount_to_receive = max(0, $receiver->amount_to_receive - $match->amount);
                $new_count = $receiver->pledges_to_receive - 1;

                if ($new_amount_to_receive <= 0) {
                    // User has received their full double amount, remove from queue
                    $query = "UPDATE users SET
                              pledges_to_receive = 0,
                              amount_to_receive = 0,
                              updated_at = NOW()
                              WHERE id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $match->receiver_id);
                    $stmt->execute();
                } else {
                    // User still needs more money, keep at front of queue
                    $query = "UPDATE users SET
                              pledges_to_receive = :new_count,
                              amount_to_receive = :new_amount
                              WHERE id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':new_count', $new_count);
                    $stmt->bindParam(':new_amount', $new_amount_to_receive);
                    $stmt->bindParam(':user_id', $match->receiver_id);
                    $stmt->execute();
                }
            } else {
                // Legacy user - migrate to amount-aware system
                $default_pledge_amount = 20; // Default amount for legacy users
                $double_amount = $default_pledge_amount * 2;
                $new_amount_to_receive = max(0, $double_amount - $match->amount);
                $new_count = $receiver->pledges_to_receive - 1;

                if ($new_amount_to_receive <= 0) {
                    // User has received estimated double amount, remove from queue
                    $query = "UPDATE users SET
                              pledges_to_receive = 0,
                              amount_to_receive = 0,
                              original_pledge_amount = :default_amount,
                              updated_at = NOW()
                              WHERE id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':default_amount', $default_pledge_amount);
                    $stmt->bindParam(':user_id', $match->receiver_id);
                    $stmt->execute();
                } else {
                    // Migrate to amount-aware system
                    $query = "UPDATE users SET
                              pledges_to_receive = :new_count,
                              amount_to_receive = :new_amount,
                              original_pledge_amount = :default_amount
                              WHERE id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':new_count', $new_count);
                    $stmt->bindParam(':new_amount', $new_amount_to_receive);
                    $stmt->bindParam(':default_amount', $default_pledge_amount);
                    $stmt->bindParam(':user_id', $match->receiver_id);
                    $stmt->execute();
                }
            }
        }

        // Get the pledge amount
        $query = "SELECT amount FROM pledges WHERE id = :pledge_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':pledge_id', $match->pledge_id);
        $stmt->execute();
        $pledge = $stmt->fetch(PDO::FETCH_OBJ);
        $pledge_amount = $pledge ? $pledge->amount : 0;

        // Create notifications
        create_notification($match->sender_id, 'Pledge Completed',
            'Your pledge has been confirmed by the receiver. You are now in queue to receive 2 pledges.', 'pledge', $db);
        create_notification($match->receiver_id, 'Pledge Received',
            'You have confirmed receipt of GHS ' . $pledge_amount . '. ' .
            (isset($new_count) && $new_count > 0 ? "You are still in queue to receive $new_count more pledge(s)." : "You have received all your pledges."),
            'pledge', $db);

        // Commit transaction
        $db->commit();

        return [
            'status' => true,
            'message' => 'Pledge payment confirmed successfully'
        ];
    } catch (Exception $e) {
        // Rollback transaction
        $db->rollBack();

        return [
            'status' => false,
            'message' => 'Error confirming pledge payment: ' . $e->getMessage()
        ];
    }
}

/**
 * Process a user receiving a pledge
 *
 * @param PDO $db Database connection
 * @param int $match_id Match ID
 * @return array Result with status and message
 */
function process_received_pledge($db, $match_id) {
    // Get match details
    $query = "SELECT * FROM matches WHERE id = :match_id AND status = 'completed'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':match_id', $match_id);
    $stmt->execute();
    $match = $stmt->fetch(PDO::FETCH_OBJ);

    if (!$match) {
        return [
            'status' => false,
            'message' => 'Match not found or not completed'
        ];
    }

    // Start transaction
    $db->beginTransaction();

    try {
        // Get receiver's current queue status
        $query = "SELECT pledges_to_receive, amount_to_receive, original_pledge_amount FROM users WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $match->receiver_id);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_OBJ);

        if (!$user || $user->pledges_to_receive <= 0) {
            // User is not in queue or has received all pledges
            $db->rollBack();
            return [
                'status' => false,
                'message' => 'User is not in queue to receive pledges'
            ];
        }

        // Get the match amount
        $query = "SELECT amount FROM matches WHERE id = :match_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':match_id', $match_id);
        $stmt->execute();
        $match_details = $stmt->fetch(PDO::FETCH_OBJ);
        $match_amount = $match_details ? $match_details->amount : 0;

        // Use amount-aware logic if available
        if ($user->amount_to_receive > 0) {
            $new_amount_to_receive = max(0, $user->amount_to_receive - $match_amount);
            $new_count = $user->pledges_to_receive - 1;

            if ($new_amount_to_receive <= 0) {
                // User has received their full double amount, remove from queue
                $query = "UPDATE users SET
                          pledges_to_receive = 0,
                          amount_to_receive = 0,
                          updated_at = NOW()
                          WHERE id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $match->receiver_id);
                $stmt->execute();
                $new_count = 0;
            } else {
                // User still needs more money, keep at front of queue
                $query = "UPDATE users SET
                          pledges_to_receive = :new_count,
                          amount_to_receive = :new_amount
                          WHERE id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':new_count', $new_count);
                $stmt->bindParam(':new_amount', $new_amount_to_receive);
                $stmt->bindParam(':user_id', $match->receiver_id);
                $stmt->execute();
            }
        } else {
            // Legacy user - migrate to amount-aware system
            $default_pledge_amount = 20; // Default amount for legacy users
            $double_amount = $default_pledge_amount * 2;
            $new_amount_to_receive = max(0, $double_amount - $match_amount);
            $new_count = $user->pledges_to_receive - 1;

            if ($new_amount_to_receive <= 0) {
                // User has received estimated double amount, remove from queue
                $query = "UPDATE users SET
                          pledges_to_receive = 0,
                          amount_to_receive = 0,
                          original_pledge_amount = :default_amount,
                          updated_at = NOW()
                          WHERE id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':default_amount', $default_pledge_amount);
                $stmt->bindParam(':user_id', $match->receiver_id);
                $stmt->execute();
                $new_count = 0;
            } else {
                // Migrate to amount-aware system
                $query = "UPDATE users SET
                          pledges_to_receive = :new_count,
                          amount_to_receive = :new_amount,
                          original_pledge_amount = :default_amount
                          WHERE id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':new_count', $new_count);
                $stmt->bindParam(':new_amount', $new_amount_to_receive);
                $stmt->bindParam(':default_amount', $default_pledge_amount);
                $stmt->bindParam(':user_id', $match->receiver_id);
                $stmt->execute();
            }
        }

        // Create notification
        create_notification($match->receiver_id, 'Pledge Received',
            'You have received a pledge of GHS ' . $match_amount . '. ' .
            ($new_count > 0 ? "You are still in queue to receive $new_count more pledge(s)." : "You have received all your pledges."),
            'pledge', $db);

        // Commit transaction
        $db->commit();

        return [
            'status' => true,
            'message' => 'Received pledge processed successfully',
            'pledges_remaining' => $new_count
        ];
    } catch (Exception $e) {
        // Rollback transaction
        $db->rollBack();

        return [
            'status' => false,
            'message' => 'Error processing received pledge: ' . $e->getMessage()
        ];
    }
}

/**
 * Get users in the receivers queue with amount information
 *
 * @param PDO $db Database connection
 * @return array List of users in queue
 */
function get_receivers_queue($db) {
    $query = "SELECT u.id, u.name, u.email, u.pledges_to_receive,
                     u.amount_to_receive, u.original_pledge_amount
              FROM users u
              WHERE u.pledges_to_receive > 0
              ORDER BY u.updated_at ASC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_OBJ);
}

/**
 * Check if a user is eligible to make a pledge
 *
 * @param PDO $db Database connection
 * @param int $user_id User ID
 * @return array Result with status and message
 */
function is_eligible_to_pledge($db, $user_id) {
    // Platform fee in tokens
    $platform_fee = 10;

    // Check if user has any pending or matched pledges
    $query = "SELECT COUNT(*) as count FROM pledges
              WHERE user_id = :user_id AND status IN ('pending', 'matched')";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_OBJ);

    // User is not eligible if they have pending or matched pledges
    if ($result->count > 0) {
        return [
            'eligible' => false,
            'message' => 'You already have a pending or matched pledge. Please complete it before making a new pledge.'
        ];
    }

    // Check if user is in the queue to receive pledges and get token balance
    $query = "SELECT pledges_to_receive, token_balance FROM users WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_OBJ);

    // User is not eligible if they are in the queue to receive pledges
    if ($user && $user->pledges_to_receive > 0) {
        return [
            'eligible' => false,
            'message' => 'You are currently in the queue to receive pledges. You cannot make a new pledge until you have received all your pledges.'
        ];
    }

    // Check if user has enough tokens for the platform fee
    if (!$user || $user->token_balance < $platform_fee) {
        return [
            'eligible' => false,
            'message' => 'You do not have enough tokens for the platform fee. You need at least ' . $platform_fee . ' tokens to make a pledge.'
        ];
    }

    return [
        'eligible' => true,
        'message' => 'You are eligible to make a pledge.'
    ];
}

/**
 * Get pledge statistics for a user
 *
 * @param PDO $db Database connection
 * @param int $user_id User ID
 * @return object Statistics object
 */
function get_user_pledge_stats($db, $user_id) {
    // Get pledges made
    $query = "SELECT COUNT(*) as count FROM pledges
              WHERE user_id = :user_id AND status = 'completed'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $pledges_made = $stmt->fetch(PDO::FETCH_OBJ)->count;

    // Get pledges received
    $query = "SELECT COUNT(*) as count FROM matches
              WHERE receiver_id = :user_id AND status = 'completed'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $pledges_received = $stmt->fetch(PDO::FETCH_OBJ)->count;

    // Get pledges to receive
    $query = "SELECT pledges_to_receive FROM users WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_OBJ);
    $pledges_to_receive = $user ? $user->pledges_to_receive : 0;

    return (object) [
        'pledges_made' => $pledges_made,
        'pledges_received' => $pledges_received,
        'pledges_to_receive' => $pledges_to_receive
    ];
}

/**
 * Find the best receiver for a pledge using amount-aware matching
 *
 * @param PDO $db Database connection
 * @param int $sender_id Sender user ID (to exclude from results)
 * @param float $pledge_amount Amount of the pledge
 * @return object|false Receiver object or false if none found
 */
function find_best_receiver($db, $sender_id, $pledge_amount) {
    // First, check if we have any users with proper amount tracking
    $query = "SELECT COUNT(*) as count FROM users
              WHERE pledges_to_receive > 0 AND amount_to_receive > 0";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $amount_aware_users = $stmt->fetch(PDO::FETCH_OBJ)->count;

    // If no users have amount tracking, fall back to legacy system
    if ($amount_aware_users == 0) {
        $query = "SELECT u.id, 0 as amount_to_receive, 0 as original_pledge_amount
                  FROM users u
                  WHERE u.id != :sender_id
                  AND u.pledges_to_receive > 0
                  AND EXISTS (
                      SELECT 1 FROM pledges
                      WHERE user_id = u.id AND status = 'completed'
                  )
                  ORDER BY u.updated_at ASC
                  LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':sender_id', $sender_id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ) ?: false;
    }

    // Priority 1: Exact amount match (receiver needs exactly this pledge amount)
    $query = "SELECT u.id, u.amount_to_receive, u.original_pledge_amount
              FROM users u
              WHERE u.id != :sender_id
              AND u.amount_to_receive = :pledge_amount
              AND u.pledges_to_receive > 0
              AND EXISTS (
                  SELECT 1 FROM pledges
                  WHERE user_id = u.id AND status = 'completed'
              )
              ORDER BY u.updated_at ASC
              LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':sender_id', $sender_id);
    $stmt->bindParam(':pledge_amount', $pledge_amount);
    $stmt->execute();
    $receiver = $stmt->fetch(PDO::FETCH_OBJ);

    if ($receiver) {
        return $receiver;
    }

    // Priority 2: Partial match (receiver needs more than this pledge amount)
    $query = "SELECT u.id, u.amount_to_receive, u.original_pledge_amount
              FROM users u
              WHERE u.id != :sender_id
              AND u.amount_to_receive > :pledge_amount
              AND u.pledges_to_receive > 0
              AND EXISTS (
                  SELECT 1 FROM pledges
                  WHERE user_id = u.id AND status = 'completed'
              )
              ORDER BY u.updated_at ASC
              LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':sender_id', $sender_id);
    $stmt->bindParam(':pledge_amount', $pledge_amount);
    $stmt->execute();
    $receiver = $stmt->fetch(PDO::FETCH_OBJ);

    if ($receiver) {
        return $receiver;
    }

    // Priority 3: Over-match (receiver needs less, but we can partially fulfill)
    // Only allow this if the receiver needs at least 50% of the pledge amount
    $min_needed = $pledge_amount * 0.5;
    $query = "SELECT u.id, u.amount_to_receive, u.original_pledge_amount
              FROM users u
              WHERE u.id != :sender_id
              AND u.amount_to_receive < :pledge_amount
              AND u.amount_to_receive >= :min_needed
              AND u.pledges_to_receive > 0
              AND EXISTS (
                  SELECT 1 FROM pledges
                  WHERE user_id = u.id AND status = 'completed'
              )
              ORDER BY u.updated_at ASC
              LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':sender_id', $sender_id);
    $stmt->bindParam(':pledge_amount', $pledge_amount);
    $stmt->bindParam(':min_needed', $min_needed);
    $stmt->execute();
    $receiver = $stmt->fetch(PDO::FETCH_OBJ);

    return $receiver ?: false;
}

/**
 * Process amount-aware match between pledge and receiver
 *
 * @param PDO $db Database connection
 * @param object $pledge Pledge object
 * @param object $receiver Receiver object
 * @return array Result with status and receiver remaining amount
 */
function process_amount_aware_match($db, $pledge, $receiver) {
    // Check if this is a legacy user (no amount tracking)
    if ($receiver->amount_to_receive <= 0) {
        // Migrate legacy user to amount-aware system
        $default_pledge_amount = 20; // Default amount for legacy users
        $double_amount = $default_pledge_amount * 2;
        $new_amount_to_receive = max(0, $double_amount - $pledge->amount);
        $new_count = max(0, $receiver->pledges_to_receive - 1);

        if ($new_amount_to_receive <= 0) {
            // User has received estimated double amount, remove from queue
            $query = "UPDATE users SET
                      pledges_to_receive = 0,
                      amount_to_receive = 0,
                      original_pledge_amount = :default_amount,
                      updated_at = NOW()
                      WHERE id = :user_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':default_amount', $default_pledge_amount);
            $stmt->bindParam(':user_id', $receiver->id);
            $stmt->execute();

            $receiver_remaining = 0;
        } else {
            // Migrate to amount-aware system and keep in queue
            $query = "UPDATE users SET
                      pledges_to_receive = :new_count,
                      amount_to_receive = :new_amount,
                      original_pledge_amount = :default_amount
                      WHERE id = :user_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':new_count', $new_count);
            $stmt->bindParam(':new_amount', $new_amount_to_receive);
            $stmt->bindParam(':default_amount', $default_pledge_amount);
            $stmt->bindParam(':user_id', $receiver->id);
            $stmt->execute();

            $receiver_remaining = $new_amount_to_receive;
        }

        return [
            'status' => true,
            'receiver_remaining' => $receiver_remaining
        ];
    }

    // Amount-aware processing for new users
    $new_amount_to_receive = max(0, $receiver->amount_to_receive - $pledge->amount);
    $new_count = $receiver->pledges_to_receive - 1;

    // Update receiver's queue status based on amount-aware logic only
    if ($new_amount_to_receive <= 0) {
        // User has received their full double amount, remove from queue
        $query = "UPDATE users SET
                  pledges_to_receive = 0,
                  amount_to_receive = 0,
                  updated_at = NOW()
                  WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $receiver->id);
        $stmt->execute();

        $receiver_remaining = 0;
    } else {
        // User still needs more money to reach double amount
        // Keep them at front of queue for consecutive receiving
        // Only update timestamp after they've received their full amount
        $query = "UPDATE users SET
                  pledges_to_receive = :new_count,
                  amount_to_receive = :new_amount
                  WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':new_count', $new_count);
        $stmt->bindParam(':new_amount', $new_amount_to_receive);
        $stmt->bindParam(':user_id', $receiver->id);
        $stmt->execute();

        $receiver_remaining = $new_amount_to_receive;
    }

    return [
        'status' => true,
        'receiver_remaining' => $receiver_remaining
    ];
}
?>
